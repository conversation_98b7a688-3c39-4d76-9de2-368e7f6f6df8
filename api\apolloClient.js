import { ApolloClient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { gql } from '@apollo/client';

// GraphQL mutation for token refresh
const REFRESH_TOKEN_MUTATION = gql`
  mutation RefreshToken {
    refreshToken {
      token
      agent {
        hiveId
        username
        role
        clearance
      }
    }
  }
`;

const httpLink = createHttpLink({
  uri: 'http://localhost:4000/graphql',
  credentials: 'include'
});

// Auth link that adds CSRF protection header
const authLink = setContext((_, { headers }) => {
  return {
    headers: {
      ...headers,
      'X-Requested-With': 'XMLHttpRequest', // CSRF protection
    }
  }
});

// Error link to handle token refresh
const errorLink = onError(({ graphQLErrors, networkError, operation, forward }) => {
  if (graphQLErrors) {
    for (let err of graphQLErrors) {
      if (err.extensions?.code === 'UNAUTHENTICATED') {
        // Try to refresh token
        return new Promise((resolve, reject) => {
          client.mutate({
            mutation: REFRESH_TOKEN_MUTATION,
            errorPolicy: 'ignore'
          }).then((result) => {
            if (result.data?.refreshToken) {
              // Token refreshed successfully, retry the original operation
              resolve(forward(operation));
            } else {
              // Refresh failed, redirect to login
              window.location.href = '/login';
              reject(new Error('Authentication failed'));
            }
          }).catch(() => {
            // Refresh failed, redirect to login
            window.location.href = '/login';
            reject(new Error('Authentication failed'));
          });
        });
      }
    }
  }

  if (networkError) {
    console.error('Network error:', networkError);
  }
});

const client = new ApolloClient({
  link: from([errorLink, authLink, httpLink]),
  cache: new InMemoryCache(),
});

export default client;