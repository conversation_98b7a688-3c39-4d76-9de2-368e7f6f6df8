{"name": "keystone", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@apollo/client": "^3.13.8", "cookie-parser": "^1.4.7", "express-rate-limit": "^7.5.1", "graphql": "^16.11.0", "lucide-react": "^0.503.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^6.14.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.1", "vite": "^4.4.0"}}