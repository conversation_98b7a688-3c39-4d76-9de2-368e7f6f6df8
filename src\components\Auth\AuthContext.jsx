import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useMutation } from '@apollo/client';
import client from '../../../api/apolloClient';
import { REFRESH_TOKEN_MUTATION, LOGOUT_MUTATION } from '../../../api/mutations';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);

  const [refreshTokenMutation] = useMutation(REFRESH_TOKEN_MUTATION);
  const [logoutMutation] = useMutation(LOGOUT_MUTATION);

  // Check authentication status on app load
  const checkAuthStatus = useCallback(async () => {
    // Prevent multiple simultaneous auth checks
    if (isCheckingAuth) return;

    setIsCheckingAuth(true);
    try {
      const result = await refreshTokenMutation();
      if (result.data?.refreshToken) {
        setUser(result.data.refreshToken.agent);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      // Don't log rate limiting errors as they're expected
      if (!error.message?.includes('429') && !error.message?.includes('Too Many Requests')) {
        console.error('Auth check failed:', error);
      }
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
      setIsCheckingAuth(false);
    }
  }, [refreshTokenMutation, isCheckingAuth]);

  // Check auth status on mount
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  // Set up automatic token refresh
  useEffect(() => {
    if (!isAuthenticated) return;

    // Refresh token every 10 minutes (access token expires in 15 minutes)
    const refreshInterval = setInterval(async () => {
      try {
        const result = await refreshTokenMutation();
        if (result.data?.refreshToken) {
          setUser(result.data.refreshToken.agent);
        } else {
          // Refresh failed, user needs to login again
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        // Don't log rate limiting errors, and be less aggressive on failures
        if (!error.message?.includes('429') && !error.message?.includes('Too Many Requests')) {
          console.error('Token refresh failed:', error);
        }
        // Only logout on non-rate-limit errors
        if (!error.message?.includes('429')) {
          setUser(null);
          setIsAuthenticated(false);
        }
      }
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(refreshInterval);
  }, [isAuthenticated, refreshTokenMutation]);

  const login = (userData, authToken) => {
    setUser(userData);
    setIsAuthenticated(true);
    // Note: authToken is still provided for backward compatibility but not stored
  };

  const logout = async () => {
    try {
      await logoutMutation();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      if (client) {
        client.resetStore();  // Clear client cache
      }
    }
  };

  const isAuthenticatedFn = () => {
    return isAuthenticated;
  };

  const isAdmin = () => {
    return user?.role === 'ADMIN';
  };

  const isDeveloper = () => {
    return user?.role === 'DEVELOPER';
  };

  const hasClearance = (level) => {
    if (!user?.clearance) return false;
    return user.clearance >= level;
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated: isAuthenticatedFn,
      isAdmin,
      isDeveloper,
      hasClearance,
      // Deprecated: keeping for backward compatibility
      token: null
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}