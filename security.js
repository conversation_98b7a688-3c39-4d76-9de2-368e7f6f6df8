const rateLimit = require('express-rate-limit');

// Rate limiting for authentication endpoints
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs for auth endpoints
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests
  skipSuccessfulRequests: true,
  // Custom key generator to include user agent for better tracking
  keyGenerator: (req) => {
    return `${req.ip}-${req.get('User-Agent') || 'unknown'}`;
  }
});

// Rate limiting for refresh token endpoint
const refreshTokenRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 20, // Allow more refresh attempts as they're more frequent, especially in development
  message: {
    error: 'Too many token refresh attempts, please try again later.',
    retryAfter: '1 minute'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return `${req.ip}-${req.get('User-Agent') || 'unknown'}`;
  }
});

// General API rate limiting
const generalRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests, please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Content Security Policy (basic)
  res.setHeader('Content-Security-Policy', "default-src 'self'");
  
  next();
};

// CSRF protection for cookie-based auth
const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET requests and preflight OPTIONS
  if (req.method === 'GET' || req.method === 'OPTIONS') {
    return next();
  }

  // For GraphQL mutations that use cookies, check for custom header
  if (req.cookies?.accessToken || req.cookies?.refreshToken) {
    const csrfHeader = req.get('X-Requested-With');
    if (!csrfHeader || csrfHeader !== 'XMLHttpRequest') {
      return res.status(403).json({
        error: 'CSRF protection: Missing required header'
      });
    }
  }

  next();
};

// Middleware to detect and handle authentication-related operations
const authOperationDetector = (req, res, next) => {
  // Check if this is a GraphQL request with auth operations
  if (req.body && req.body.query) {
    const query = req.body.query.toLowerCase();
    
    // Apply auth rate limiting for login operations
    if (query.includes('mutation') && query.includes('login')) {
      return authRateLimit(req, res, next);
    }
    
    // Apply refresh token rate limiting
    if (query.includes('mutation') && query.includes('refreshtoken')) {
      return refreshTokenRateLimit(req, res, next);
    }
  }
  
  // Apply general rate limiting for other operations
  return generalRateLimit(req, res, next);
};

module.exports = {
  authRateLimit,
  refreshTokenRateLimit,
  generalRateLimit,
  securityHeaders,
  csrfProtection,
  authOperationDetector
};
